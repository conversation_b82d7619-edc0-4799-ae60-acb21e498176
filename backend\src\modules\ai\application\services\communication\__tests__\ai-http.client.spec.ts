import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { AiHttpClient } from '../ai-http.client';
import axios, { AxiosResponse, AxiosError } from 'axios';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('AiHttpClient', () => {
  let client: AiHttpClient;
  let configService: jest.Mocked<ConfigService>;
  let mockAxiosInstance: jest.Mocked<any>;

  const mockAxiosResponse = <T>(data: T, status = 200): AxiosResponse<T> => ({
    data,
    status,
    statusText: 'OK',
    headers: {},
    config: {} as any,
  });

  const mockAxiosError = (status: number, message: string): AxiosError => ({
    name: 'AxiosError',
    message,
    response: {
      status,
      statusText: message,
      data: { message },
      headers: {},
      config: {} as any,
    },
    config: {} as any,
    isAxiosError: true,
    toJSON: () => ({}),
  });

  beforeEach(async () => {
    // Reset mocks
    jest.clearAllMocks();

    // Mock axios instance
    mockAxiosInstance = {
      request: jest.fn(),
    };

    mockedAxios.create.mockReturnValue(mockAxiosInstance);

    const mockConfigService = {
      get: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AiHttpClient,
        { provide: ConfigService, useValue: mockConfigService },
      ],
    }).compile();

    client = module.get<AiHttpClient>(AiHttpClient);
    configService = module.get(ConfigService);

    // Setup default config values
    configService.get.mockImplementation((key: string, defaultValue?: any) => {
      const config = {
        'ai.http.timeout': 30000,
        'ai.http.retries': 3,
      };
      return config[key] || defaultValue;
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('sendAnalysisRequest', () => {
    it('should send analysis request successfully', async () => {
      const mockResponse = {
        id: 'analysis-123',
        result: { threat_level: 'high', confidence: 0.95 },
        confidence: 0.95,
        model: 'threat-detector-v1',
        timestamp: new Date().toISOString(),
      };

      mockAxiosInstance.request.mockResolvedValue(mockAxiosResponse(mockResponse));

      const payload = {
        data: { event: 'suspicious_login' },
        model: 'threat-detector-v1',
      };

      const result = await client.sendAnalysisRequest('http://ai-service/analyze', payload);

      expect(result).toMatchObject({
        id: expect.any(String),
        result: { threat_level: 'high', confidence: 0.95 },
        confidence: 0.95,
        metadata: expect.objectContaining({
          model: 'threat-detector-v1',
        }),
        timestamp: expect.any(Date),
      });

      expect(mockAxiosInstance.request).toHaveBeenCalledWith(
        expect.objectContaining({
          method: 'POST',
          url: 'http://ai-service/analyze',
          data: payload,
          timeout: 30000,
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
            'User-Agent': 'Sentinel-AI-Client/1.0',
            'X-Request-ID': expect.any(String),
          }),
        })
      );
    });

    it('should handle analysis request failure', async () => {
      const error = mockAxiosError(500, 'Internal Server Error');
      mockAxiosInstance.request.mockRejectedValue(error);

      const payload = { data: { event: 'test' } };

      await expect(
        client.sendAnalysisRequest('http://ai-service/analyze', payload)
      ).rejects.toThrow('Analysis request failed');
    });

    it('should use custom timeout and headers', async () => {
      const mockResponse = { id: 'test', result: {}, confidence: 0.8 };
      mockAxiosInstance.request.mockResolvedValue(mockAxiosResponse(mockResponse));

      const payload = { data: { event: 'test' } };
      const options = {
        timeout: 60000,
        apiKey: 'test-key',
        customHeaders: { 'X-Custom': 'value' },
      };

      await client.sendAnalysisRequest('http://ai-service/analyze', payload, options);

      expect(mockAxiosInstance.request).toHaveBeenCalledWith(
        expect.objectContaining({
          timeout: 60000,
          headers: expect.objectContaining({
            'Authorization': 'Bearer test-key',
            'X-Custom': 'value',
          }),
        })
      );
    });
  });

  describe('sendTrainingRequest', () => {
    it('should send training request successfully', async () => {
      const mockResponse = {
        jobId: 'job-123',
        status: 'completed',
        modelId: 'model-456',
        metrics: { accuracy: 0.95 },
        timestamp: new Date().toISOString(),
      };

      mockAxiosInstance.request.mockResolvedValue(mockAxiosResponse(mockResponse));

      const payload = {
        trainingData: [{ input: 'test', output: 'result' }],
        modelConfig: { type: 'classifier' },
      };

      const result = await client.sendTrainingRequest('http://ai-service/train', payload);

      expect(result).toMatchObject({
        jobId: 'job-123',
        status: 'completed',
        modelId: 'model-456',
        metrics: { accuracy: 0.95 },
        timestamp: expect.any(Date),
      });

      expect(mockAxiosInstance.request).toHaveBeenCalledWith(
        expect.objectContaining({
          method: 'POST',
          url: 'http://ai-service/train',
          data: payload,
          timeout: 300000, // 5 minutes default for training
        })
      );
    });

    it('should handle training request failure', async () => {
      const error = mockAxiosError(400, 'Invalid training data');
      mockAxiosInstance.request.mockRejectedValue(error);

      const payload = {
        trainingData: [],
        modelConfig: { type: 'classifier' },
      };

      await expect(
        client.sendTrainingRequest('http://ai-service/train', payload)
      ).rejects.toThrow('Training request failed');
    });
  });

  describe('sendPredictionRequest', () => {
    it('should send prediction request successfully', async () => {
      const mockResponse = {
        prediction: 'malicious',
        confidence: 0.87,
        alternatives: ['benign'],
        model: 'classifier-v2',
        latency: 150,
        timestamp: new Date().toISOString(),
      };

      mockAxiosInstance.request.mockResolvedValue(mockAxiosResponse(mockResponse));

      const payload = {
        input: { features: [1, 2, 3] },
        model: 'classifier-v2',
      };

      const result = await client.sendPredictionRequest('http://ai-service/predict', payload);

      expect(result).toMatchObject({
        prediction: 'malicious',
        confidence: 0.87,
        alternatives: ['benign'],
        metadata: expect.objectContaining({
          model: 'classifier-v2',
          latency: 150,
        }),
        timestamp: expect.any(Date),
      });

      expect(mockAxiosInstance.request).toHaveBeenCalledWith(
        expect.objectContaining({
          method: 'POST',
          url: 'http://ai-service/predict',
          data: payload,
          timeout: 5000, // 5 seconds default for predictions
        })
      );
    });

    it('should handle prediction request failure', async () => {
      const error = mockAxiosError(422, 'Invalid input format');
      mockAxiosInstance.request.mockRejectedValue(error);

      const payload = { input: null };

      await expect(
        client.sendPredictionRequest('http://ai-service/predict', payload)
      ).rejects.toThrow('Prediction request failed');
    });
  });

  describe('sendHealthCheckRequest', () => {
    it('should send health check request successfully', async () => {
      const mockResponse = {
        healthy: true,
        status: 'operational',
        responseTime: 45,
        version: '1.2.3',
      };

      httpService.request.mockReturnValue(of(mockAxiosResponse(mockResponse)));

      const result = await client.sendHealthCheckRequest('http://ai-service');

      expect(result).toMatchObject({
        healthy: true,
        status: 'operational',
        responseTime: 45,
        version: '1.2.3',
        timestamp: expect.any(Date),
      });

      expect(httpService.request).toHaveBeenCalledWith(
        expect.objectContaining({
          method: 'GET',
          url: 'http://ai-service/health',
          timeout: 5000,
        })
      );
    });

    it('should handle health check failure gracefully', async () => {
      const error = mockAxiosError(503, 'Service Unavailable');
      httpService.request.mockReturnValue(throwError(() => error));

      const result = await client.sendHealthCheckRequest('http://ai-service');

      expect(result).toMatchObject({
        healthy: false,
        status: 'unhealthy',
        error: expect.any(String),
        timestamp: expect.any(Date),
      });
    });

    it('should consider 200 status as healthy when no explicit healthy field', async () => {
      const mockResponse = { status: 'ok' };
      httpService.request.mockReturnValue(of(mockAxiosResponse(mockResponse, 200)));

      const result = await client.sendHealthCheckRequest('http://ai-service');

      expect(result.healthy).toBe(true);
    });
  });

  describe('sendBatchRequest', () => {
    it('should send batch request successfully', async () => {
      const mockResponse = {
        batchId: 'batch-123',
        results: [
          { id: '1', result: 'success' },
          { id: '2', result: 'success' },
        ],
        total: 2,
        successful: 2,
        failed: 0,
        processingTime: 1500,
        timestamp: new Date().toISOString(),
      };

      httpService.request.mockReturnValue(of(mockAxiosResponse(mockResponse)));

      const payloads = [
        { id: '1', data: { input: 'test1' }, type: 'analysis' },
        { id: '2', data: { input: 'test2' }, type: 'analysis' },
      ];

      const result = await client.sendBatchRequest('http://ai-service/batch', payloads);

      expect(result).toMatchObject({
        batchId: 'batch-123',
        results: expect.arrayContaining([
          { id: '1', result: 'success' },
          { id: '2', result: 'success' },
        ]),
        summary: {
          total: 2,
          successful: 2,
          failed: 0,
          processingTime: 1500,
        },
        timestamp: expect.any(Date),
      });

      expect(httpService.request).toHaveBeenCalledWith(
        expect.objectContaining({
          method: 'POST',
          url: 'http://ai-service/batch',
          data: expect.objectContaining({
            requests: payloads,
            batchId: expect.any(String),
            timestamp: expect.any(String),
          }),
          timeout: 120000, // 2 minutes default for batch
        })
      );
    });

    it('should handle batch request failure', async () => {
      const error = mockAxiosError(413, 'Payload Too Large');
      httpService.request.mockReturnValue(throwError(() => error));

      const payloads = [{ id: '1', data: {}, type: 'test' }];

      await expect(
        client.sendBatchRequest('http://ai-service/batch', payloads)
      ).rejects.toThrow('Batch request failed');
    });
  });

  describe('uploadFile', () => {
    it('should upload file successfully', async () => {
      const mockResponse = {
        fileId: 'file-123',
        filename: 'test.csv',
        size: 1024,
        url: 'http://storage/file-123',
      };

      httpService.request.mockReturnValue(of(mockAxiosResponse(mockResponse)));

      const fileBuffer = Buffer.from('test,data\n1,2\n3,4');
      const result = await client.uploadFile(
        'http://ai-service/upload',
        fileBuffer,
        'test.csv'
      );

      expect(result).toEqual(mockResponse);

      expect(httpService.request).toHaveBeenCalledWith(
        expect.objectContaining({
          method: 'POST',
          url: 'http://ai-service/upload',
          data: expect.any(FormData),
          timeout: 60000,
          headers: expect.objectContaining({
            'Content-Type': 'multipart/form-data',
          }),
        })
      );
    });

    it('should handle file upload failure', async () => {
      const error = mockAxiosError(413, 'File Too Large');
      httpService.request.mockReturnValue(throwError(() => error));

      const fileBuffer = Buffer.from('test data');

      await expect(
        client.uploadFile('http://ai-service/upload', fileBuffer, 'test.txt')
      ).rejects.toThrow('File upload failed');
    });
  });

  describe('downloadFile', () => {
    it('should download file successfully', async () => {
      const fileData = new ArrayBuffer(1024);
      const mockResponse = mockAxiosResponse(fileData);
      mockResponse.headers = {
        'content-type': 'application/octet-stream',
        'content-disposition': 'attachment; filename="downloaded.bin"',
      };

      httpService.request.mockReturnValue(of(mockResponse));

      const result = await client.downloadFile('http://ai-service/files', 'file-123');

      expect(result).toMatchObject({
        data: expect.any(Buffer),
        filename: 'downloaded.bin',
        contentType: 'application/octet-stream',
        size: 1024,
      });

      expect(httpService.request).toHaveBeenCalledWith(
        expect.objectContaining({
          method: 'GET',
          url: 'http://ai-service/files/file-123',
          responseType: 'arraybuffer',
          timeout: 60000,
        })
      );
    });

    it('should handle file download failure', async () => {
      const error = mockAxiosError(404, 'File Not Found');
      httpService.request.mockReturnValue(throwError(() => error));

      await expect(
        client.downloadFile('http://ai-service/files', 'nonexistent')
      ).rejects.toThrow('File download failed');
    });

    it('should use default filename when content-disposition header is missing', async () => {
      const fileData = new ArrayBuffer(512);
      const mockResponse = mockAxiosResponse(fileData);
      mockResponse.headers = { 'content-type': 'text/plain' };

      httpService.request.mockReturnValue(of(mockResponse));

      const result = await client.downloadFile('http://ai-service/files', 'file-123');

      expect(result.filename).toBe('download');
    });
  });

  describe('streamRequest', () => {
    it('should handle stream request successfully', async () => {
      const mockStream = new ReadableStream();
      httpService.request.mockReturnValue(of(mockAxiosResponse(mockStream)));

      const payload = { data: 'streaming test' };
      const result = await client.streamRequest('http://ai-service/stream', payload);

      expect(result).toBe(mockStream);

      expect(httpService.request).toHaveBeenCalledWith(
        expect.objectContaining({
          method: 'POST',
          url: 'http://ai-service/stream',
          data: payload,
          responseType: 'stream',
        })
      );
    });

    it('should handle stream request failure', async () => {
      const error = mockAxiosError(500, 'Stream Error');
      httpService.request.mockReturnValue(throwError(() => error));

      const payload = { data: 'test' };

      await expect(
        client.streamRequest('http://ai-service/stream', payload)
      ).rejects.toThrow('Stream request failed');
    });
  });

  describe('error handling', () => {
    it('should handle timeout errors', async () => {
      const timeoutError = new Error('timeout of 30000ms exceeded');
      timeoutError['code'] = 'ECONNABORTED';
      httpService.request.mockReturnValue(throwError(() => timeoutError));

      await expect(
        client.sendAnalysisRequest('http://ai-service/analyze', { data: {} })
      ).rejects.toThrow('Request timeout');
    });

    it('should handle connection refused errors', async () => {
      const connectionError = new Error('connect ECONNREFUSED');
      connectionError['code'] = 'ECONNREFUSED';
      httpService.request.mockReturnValue(throwError(() => connectionError));

      await expect(
        client.sendAnalysisRequest('http://ai-service/analyze', { data: {} })
      ).rejects.toThrow('Connection refused');
    });

    it('should handle unknown errors', async () => {
      const unknownError = new Error('Unknown error');
      httpService.request.mockReturnValue(throwError(() => unknownError));

      await expect(
        client.sendAnalysisRequest('http://ai-service/analyze', { data: {} })
      ).rejects.toThrow('Unknown error');
    });
  });

  describe('request configuration', () => {
    it('should generate unique request IDs', async () => {
      const mockResponse = { id: 'test', result: {}, confidence: 0.5 };
      httpService.request.mockReturnValue(of(mockAxiosResponse(mockResponse)));

      await client.sendAnalysisRequest('http://ai-service/analyze', { data: {} });
      await client.sendAnalysisRequest('http://ai-service/analyze', { data: {} });

      const calls = httpService.request.mock.calls;
      const requestId1 = calls[0][0].headers['X-Request-ID'];
      const requestId2 = calls[1][0].headers['X-Request-ID'];

      expect(requestId1).toBeDefined();
      expect(requestId2).toBeDefined();
      expect(requestId1).not.toBe(requestId2);
    });

    it('should use configured timeout and retries', () => {
      configService.get.mockImplementation((key: string, defaultValue?: any) => {
        const config = {
          'ai.http.timeout': 45000,
          'ai.http.retries': 5,
        };
        return config[key] || defaultValue;
      });

      // Create new instance to pick up new config
      const newClient = new AiHttpClient(httpService, configService);

      const mockResponse = { id: 'test', result: {}, confidence: 0.5 };
      httpService.request.mockReturnValue(of(mockAxiosResponse(mockResponse)));

      newClient.sendAnalysisRequest('http://ai-service/analyze', { data: {} });

      expect(httpService.request).toHaveBeenCalledWith(
        expect.objectContaining({
          timeout: 45000,
        })
      );
    });
  });
});