import { v4 as uuidv4 } from 'uuid';

/**
 * Base Integration Event
 * 
 * Abstract base class for all integration events in the system
 */
export abstract class BaseIntegrationEvent {
  public readonly id: string;
  public readonly timestamp: Date;
  public readonly version: string;
  public readonly source: string;
  public readonly eventType: string;
  protected readonly eventData: any;

  constructor(
    eventType: string,
    eventData: any,
    source: string = 'sentinel-backend',
    version: string = '1.0'
  ) {
    this.id = uuidv4();
    this.timestamp = new Date();
    this.eventType = eventType;
    this.eventData = eventData;
    this.source = source;
    this.version = version;
  }

  /**
   * Get event ID
   */
  getId(): string {
    return this.id;
  }

  /**
   * Get event timestamp
   */
  getTimestamp(): Date {
    return this.timestamp;
  }

  /**
   * Get event type
   */
  getEventType(): string {
    return this.eventType;
  }

  /**
   * Get event source
   */
  getSource(): string {
    return this.source;
  }

  /**
   * Get event version
   */
  getVersion(): string {
    return this.version;
  }

  /**
   * Get event data
   */
  getEventData(): any {
    return this.eventData;
  }

  /**
   * Convert event to JSON
   */
  toJSON(): any {
    return {
      id: this.id,
      timestamp: this.timestamp.toISOString(),
      eventType: this.eventType,
      source: this.source,
      version: this.version,
      data: this.eventData,
    };
  }

  /**
   * Get event metadata
   */
  getMetadata(): any {
    return {
      id: this.id,
      timestamp: this.timestamp,
      eventType: this.eventType,
      source: this.source,
      version: this.version,
    };
  }

  /**
   * Validate event data
   */
  abstract validate(): boolean;

  /**
   * Get event priority
   */
  abstract getPriority(): 'low' | 'medium' | 'high' | 'critical';

  /**
   * Check if event requires immediate processing
   */
  abstract requiresImmediateProcessing(): boolean;

  /**
   * Get event correlation ID for tracking related events
   */
  getCorrelationId(): string | null {
    return this.eventData?.correlationId || null;
  }

  /**
   * Get event tags for categorization
   */
  getTags(): string[] {
    return this.eventData?.tags || [];
  }

  /**
   * Check if event is retryable in case of processing failure
   */
  isRetryable(): boolean {
    return true; // Default to retryable
  }

  /**
   * Get maximum retry attempts
   */
  getMaxRetryAttempts(): number {
    return 3; // Default retry attempts
  }

  /**
   * Get retry delay in milliseconds
   */
  getRetryDelay(): number {
    return 1000; // Default 1 second delay
  }
}
