import { BaseIntegrationEvent } from '../../../../../shared-kernel/integration/base-integration-event';

/**
 * Notification Channel Types
 */
export enum NotificationChannel {
  EMAIL = 'email',
  SMS = 'sms',
  SLACK = 'slack',
  TEAMS = 'teams',
  WEBHOOK = 'webhook',
  PUSH = 'push',
  PHONE = 'phone',
  PAGER = 'pager',
  DASHBOARD = 'dashboard',
}

/**
 * Notification Priority Levels
 */
export enum NotificationPriority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  URGENT = 'urgent',
  CRITICAL = 'critical',
}

/**
 * Notification Integration Event Data
 */
export interface NotificationIntegrationEventData {
  /** Notification type */
  notificationType: 'security_alert' | 'incident_update' | 'vulnerability_alert' | 'system_status' | 'compliance_alert';
  /** Notification content */
  notification: {
    /** Notification ID */
    id: string;
    /** Notification title */
    title: string;
    /** Notification message */
    message: string;
    /** Priority level */
    priority: NotificationPriority;
    /** Urgency level */
    urgency: 'low' | 'medium' | 'high' | 'critical';
    /** Category */
    category: string;
    /** Tags */
    tags: string[];
    /** Rich content */
    richContent?: {
      html: string;
      markdown: string;
      attachments: Array<{
        name: string;
        url: string;
        type: string;
        size: number;
      }>;
    };
    /** Action buttons */
    actions?: Array<{
      label: string;
      action: string;
      url?: string;
      style: 'primary' | 'secondary' | 'danger';
    }>;
    /** Expiration */
    expiresAt?: string;
  };
  /** Recipients */
  recipients: Array<{
    /** Recipient type */
    type: 'user' | 'group' | 'role' | 'external';
    /** Recipient identifier */
    id: string;
    /** Recipient name */
    name: string;
    /** Contact information */
    contact: {
      email?: string;
      phone?: string;
      slackUserId?: string;
      teamsUserId?: string;
    };
    /** Preferred channels */
    preferredChannels: NotificationChannel[];
    /** Escalation rules */
    escalation?: {
      enabled: boolean;
      timeoutMinutes: number;
      escalateTo: string[];
    };
  }>;
  /** Delivery channels */
  channels: Array<{
    /** Channel type */
    channel: NotificationChannel;
    /** Channel configuration */
    config: {
      /** Channel-specific settings */
      settings: Record<string, any>;
      /** Template to use */
      template?: string;
      /** Retry configuration */
      retry: {
        maxAttempts: number;
        backoffMultiplier: number;
        maxDelay: number;
      };
    };
    /** Delivery status */
    status: 'pending' | 'sent' | 'delivered' | 'failed' | 'expired';
    /** Delivery attempts */
    attempts: number;
    /** Last attempt timestamp */
    lastAttempt?: string;
    /** Error details */
    error?: string;
  }>;
  /** Source information */
  source: {
    /** Source system */
    system: string;
    /** Source component */
    component: string;
    /** Source event ID */
    eventId?: string;
    /** Source entity ID */
    entityId?: string;
    /** Source entity type */
    entityType?: string;
  };
  /** Delivery tracking */
  delivery: {
    /** Delivery ID */
    deliveryId: string;
    /** Scheduled delivery time */
    scheduledAt: string;
    /** Actual delivery time */
    deliveredAt?: string;
    /** Delivery duration (ms) */
    deliveryDuration?: number;
    /** Read status */
    readStatus: {
      isRead: boolean;
      readAt?: string;
      readBy?: string;
    };
    /** Response tracking */
    responses: Array<{
      recipientId: string;
      action: string;
      timestamp: string;
      metadata?: Record<string, any>;
    }>;
  };
  /** Integration metadata */
  integration: {
    /** Integration ID */
    integrationId: string;
    /** Processing timestamp */
    processedAt: string;
    /** Processing duration (ms) */
    processingDuration: number;
    /** Template applied */
    templateApplied?: string;
    /** Personalization applied */
    personalizationApplied: boolean;
    /** Compliance checks */
    complianceChecks: {
      gdprCompliant: boolean;
      retentionPeriod: number;
      consentRequired: boolean;
      consentObtained: boolean;
    };
  };
}

/**
 * Notification Integration Event
 * 
 * Published when notifications need to be sent through various channels
 * for security alerts, incident updates, and system notifications.
 * 
 * Key use cases:
 * - Send security alerts to SOC team
 * - Notify stakeholders of incident updates
 * - Alert on critical vulnerabilities
 * - System status notifications
 * - Compliance and regulatory notifications
 * - Escalation notifications
 */
export class NotificationIntegrationEvent extends BaseIntegrationEvent<NotificationIntegrationEventData> {
  constructor(
    eventData: NotificationIntegrationEventData,
    options?: {
      eventId?: string;
      correlationId?: string;
      causationId?: string;
      timestamp?: Date;
      version?: string;
      metadata?: Record<string, any>;
    }
  ) {
    super('NotificationIntegration', eventData, {
      source: 'sentinel-backend',
      version: options?.version || '1.0',
      correlationId: options?.correlationId,
      causationId: options?.causationId,
      metadata: {
        domain: 'Security',
        category: 'Notification',
        ...options?.metadata,
      },
    });
  }

  /**
   * Get notification ID
   */
  get notificationId(): string {
    const data = this.getEventData() as NotificationIntegrationEventData;
    return data.notification.id;
  }

  /**
   * Get notification title
   */
  get title(): string {
    const data = this.getEventData() as NotificationIntegrationEventData;
    return data.notification.title;
  }

  /**
   * Get notification priority
   */
  get priority(): NotificationPriority {
    const data = this.getEventData() as NotificationIntegrationEventData;
    return data.notification.priority;
  }

  /**
   * Get notification urgency
   */
  get urgency(): 'low' | 'medium' | 'high' | 'critical' {
    const data = this.getEventData() as NotificationIntegrationEventData;
    return data.notification.urgency;
  }

  /**
   * Get recipient count
   */
  get recipientCount(): number {
    const data = this.getEventData() as NotificationIntegrationEventData;
    return data.recipients.length;
  }

  /**
   * Get channel count
   */
  get channelCount(): number {
    const data = this.getEventData() as NotificationIntegrationEventData;
    return data.channels.length;
  }

  /**
   * Check if notification is high priority
   */
  isHighPriority(): boolean {
    const data = this.getEventData() as NotificationIntegrationEventData;
    return data.notification.priority === NotificationPriority.HIGH ||
           data.notification.priority === NotificationPriority.URGENT ||
           data.notification.priority === NotificationPriority.CRITICAL;
  }

  /**
   * Check if notification is urgent
   */
  isUrgent(): boolean {
    const data = this.getEventData() as NotificationIntegrationEventData;
    return data.notification.urgency === 'critical' ||
           data.notification.priority === NotificationPriority.URGENT ||
           data.notification.priority === NotificationPriority.CRITICAL;
  }

  /**
   * Check if notification has escalation
   */
  hasEscalation(): boolean {
    const data = this.getEventData() as NotificationIntegrationEventData;
    return data.recipients.some(recipient =>
      recipient.escalation?.enabled === true
    );
  }

  /**
   * Check if notification has actions
   */
  hasActions(): boolean {
    const data = this.getEventData() as NotificationIntegrationEventData;
    return !!data.notification.actions &&
           data.notification.actions.length > 0;
  }

  /**
   * Check if notification is expired
   */
  isExpired(): boolean {
    if (!this.eventData.notification.expiresAt) {
      return false;
    }
    return new Date() > new Date(this.eventData.notification.expiresAt);
  }

  /**
   * Check if notification is delivered
   */
  isDelivered(): boolean {
    return !!this.eventData.delivery.deliveredAt;
  }

  /**
   * Check if notification is read
   */
  isRead(): boolean {
    return this.eventData.delivery.readStatus.isRead;
  }

  /**
   * Get failed channels
   */
  getFailedChannels(): NotificationChannel[] {
    return this.eventData.channels
      .filter(channel => channel.status === 'failed')
      .map(channel => channel.channel);
  }

  /**
   * Get successful channels
   */
  getSuccessfulChannels(): NotificationChannel[] {
    return this.eventData.channels
      .filter(channel => channel.status === 'delivered')
      .map(channel => channel.channel);
  }

  /**
   * Get delivery success rate
   */
  getDeliverySuccessRate(): number {
    const totalChannels = this.eventData.channels.length;
    if (totalChannels === 0) return 0;

    const successfulChannels = this.getSuccessfulChannels().length;
    return (successfulChannels / totalChannels) * 100;
  }

  /**
   * Get notification actions to trigger
   */
  getNotificationActions(): string[] {
    const actions: string[] = [];

    if (this.isUrgent()) {
      actions.push('immediate_delivery', 'escalation_monitoring');
    }

    if (this.hasEscalation()) {
      actions.push('setup_escalation_timer', 'monitor_acknowledgment');
    }

    if (this.hasActions()) {
      actions.push('enable_action_tracking', 'setup_response_monitoring');
    }

    if (this.eventData.notification.expiresAt) {
      actions.push('setup_expiration_timer');
    }

    actions.push('track_delivery_status', 'log_notification_metrics');

    return actions;
  }

  /**
   * Get retry requirements
   */
  getRetryRequirements(): Array<{
    channel: NotificationChannel;
    shouldRetry: boolean;
    nextRetryAt: Date;
    maxAttempts: number;
    currentAttempts: number;
  }> {
    return this.eventData.channels.map(channel => {
      const shouldRetry = channel.status === 'failed' && 
                         channel.attempts < channel.config.retry.maxAttempts;
      
      const nextRetryAt = new Date();
      if (shouldRetry) {
        const delay = Math.min(
          channel.config.retry.backoffMultiplier ** channel.attempts * 1000,
          channel.config.retry.maxDelay
        );
        nextRetryAt.setTime(nextRetryAt.getTime() + delay);
      }

      return {
        channel: channel.channel,
        shouldRetry,
        nextRetryAt,
        maxAttempts: channel.config.retry.maxAttempts,
        currentAttempts: channel.attempts,
      };
    });
  }

  /**
   * Get escalation requirements
   */
  getEscalationRequirements(): Array<{
    recipientId: string;
    escalationEnabled: boolean;
    timeoutMinutes: number;
    escalateTo: string[];
    escalationDue: Date;
  }> {
    return this.eventData.recipients
      .filter(recipient => recipient.escalation?.enabled)
      .map(recipient => {
        const escalationDue = new Date();
        escalationDue.setMinutes(
          escalationDue.getMinutes() + (recipient.escalation?.timeoutMinutes || 60)
        );

        return {
          recipientId: recipient.id,
          escalationEnabled: true,
          timeoutMinutes: recipient.escalation?.timeoutMinutes || 60,
          escalateTo: recipient.escalation?.escalateTo || [],
          escalationDue,
        };
      });
  }

  /**
   * Get compliance requirements
   */
  getComplianceRequirements(): {
    gdprCompliant: boolean;
    retentionPeriod: number;
    consentRequired: boolean;
    consentObtained: boolean;
    dataProcessingLegal: boolean;
    auditTrailRequired: boolean;
  } {
    const compliance = this.eventData.integration.complianceChecks;

    return {
      ...compliance,
      dataProcessingLegal: compliance.gdprCompliant && 
                          (!compliance.consentRequired || compliance.consentObtained),
      auditTrailRequired: this.isHighPriority() || 
                         this.eventData.notificationType === 'compliance_alert',
    };
  }

  /**
   * Convert to external notification format
   */
  toExternalFormat(): {
    notification: {
      id: string;
      title: string;
      message: string;
      priority: string;
      urgency: string;
      category: string;
      tags: string[];
      actions?: Array<{
        label: string;
        action: string;
        url?: string;
        style: string;
      }>;
      expiresAt?: string;
    };
    recipients: Array<{
      id: string;
      name: string;
      type: string;
      contact: Record<string, string>;
      preferredChannels: string[];
    }>;
    delivery: {
      deliveryId: string;
      scheduledAt: string;
      channels: Array<{
        channel: string;
        status: string;
        attempts: number;
        lastAttempt?: string;
        error?: string;
      }>;
      readStatus: {
        isRead: boolean;
        readAt?: string;
        readBy?: string;
      };
    };
    source: {
      system: string;
      component: string;
      eventId?: string;
      entityId?: string;
      entityType?: string;
    };
    metadata: {
      integrationId: string;
      processedAt: string;
      templateApplied?: string;
      personalizationApplied: boolean;
      compliance: {
        gdprCompliant: boolean;
        retentionPeriod: number;
        consentRequired: boolean;
        consentObtained: boolean;
      };
      actions: string[];
      retryRequirements: Array<{
        channel: string;
        shouldRetry: boolean;
        nextRetryAt: string;
      }>;
      escalationRequirements: Array<{
        recipientId: string;
        timeoutMinutes: number;
        escalateTo: string[];
        escalationDue: string;
      }>;
    };
  } {
    const retryRequirements = this.getRetryRequirements();
    const escalationRequirements = this.getEscalationRequirements();

    return {
      notification: {
        id: this.eventData.notification.id,
        title: this.eventData.notification.title,
        message: this.eventData.notification.message,
        priority: this.eventData.notification.priority,
        urgency: this.eventData.notification.urgency,
        category: this.eventData.notification.category,
        tags: this.eventData.notification.tags,
        actions: this.eventData.notification.actions,
        expiresAt: this.eventData.notification.expiresAt,
      },
      recipients: this.eventData.recipients.map(recipient => ({
        id: recipient.id,
        name: recipient.name,
        type: recipient.type,
        contact: recipient.contact as Record<string, string>,
        preferredChannels: recipient.preferredChannels,
      })),
      delivery: {
        deliveryId: this.eventData.delivery.deliveryId,
        scheduledAt: this.eventData.delivery.scheduledAt,
        channels: this.eventData.channels.map(channel => ({
          channel: channel.channel,
          status: channel.status,
          attempts: channel.attempts,
          lastAttempt: channel.lastAttempt,
          error: channel.error,
        })),
        readStatus: this.eventData.delivery.readStatus,
      },
      source: this.eventData.source,
      metadata: {
        integrationId: this.eventData.integration.integrationId,
        processedAt: this.eventData.integration.processedAt,
        templateApplied: this.eventData.integration.templateApplied,
        personalizationApplied: this.eventData.integration.personalizationApplied,
        compliance: this.eventData.integration.complianceChecks,
        actions: this.getNotificationActions(),
        retryRequirements: retryRequirements.map(req => ({
          channel: req.channel,
          shouldRetry: req.shouldRetry,
          nextRetryAt: req.nextRetryAt.toISOString(),
        })),
        escalationRequirements: escalationRequirements.map(req => ({
          recipientId: req.recipientId,
          timeoutMinutes: req.timeoutMinutes,
          escalateTo: req.escalateTo,
          escalationDue: req.escalationDue.toISOString(),
        })),
      },
    };
  }

  /**
   * Create security alert notification
   */
  static createSecurityAlert(
    alertData: {
      title: string;
      message: string;
      severity: 'low' | 'medium' | 'high' | 'critical';
      category: string;
      sourceEventId?: string;
    },
    recipients: string[],
    channels: NotificationChannel[]
  ): NotificationIntegrationEvent {
    const now = new Date();
    const notificationId = `alert-${Date.now()}`;

    const priority = alertData.severity === 'critical' ? NotificationPriority.CRITICAL :
                    alertData.severity === 'high' ? NotificationPriority.HIGH :
                    alertData.severity === 'medium' ? NotificationPriority.NORMAL :
                    NotificationPriority.LOW;

    const eventData: NotificationIntegrationEventData = {
      notificationType: 'security_alert',
      notification: {
        id: notificationId,
        title: alertData.title,
        message: alertData.message,
        priority,
        urgency: alertData.severity,
        category: alertData.category,
        tags: ['security', 'alert', alertData.severity],
        actions: alertData.severity === 'critical' ? [
          {
            label: 'Acknowledge',
            action: 'acknowledge',
            style: 'primary',
          },
          {
            label: 'Escalate',
            action: 'escalate',
            style: 'danger',
          },
        ] : undefined,
      },
      recipients: recipients.map(recipientId => ({
        type: 'user',
        id: recipientId,
        name: recipientId,
        contact: {},
        preferredChannels: channels,
        escalation: alertData.severity === 'critical' ? {
          enabled: true,
          timeoutMinutes: 30,
          escalateTo: ['security_manager'],
        } : undefined,
      })),
      channels: channels.map(channel => ({
        channel,
        config: {
          settings: {},
          retry: {
            maxAttempts: 3,
            backoffMultiplier: 2,
            maxDelay: 300000, // 5 minutes
          },
        },
        status: 'pending',
        attempts: 0,
      })),
      source: {
        system: 'SecurityPlatform',
        component: 'AlertManager',
        eventId: alertData.sourceEventId,
      },
      delivery: {
        deliveryId: `delivery-${notificationId}`,
        scheduledAt: now.toISOString(),
        readStatus: {
          isRead: false,
        },
        responses: [],
      },
      integration: {
        integrationId: 'notification-service',
        processedAt: now.toISOString(),
        processingDuration: 0,
        personalizationApplied: false,
        complianceChecks: {
          gdprCompliant: true,
          retentionPeriod: 2592000, // 30 days
          consentRequired: false,
          consentObtained: true,
        },
      },
    };

    return new NotificationIntegrationEvent(eventData);
  }
}
