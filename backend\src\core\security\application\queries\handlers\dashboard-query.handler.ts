import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON>and<PERSON> } from '@nestjs/cqrs';
import { SecurityEventRepository } from '../../../domain/interfaces/repositories/security-event.repository.interface';
import { VulnerabilityRepository } from '../../../domain/interfaces/repositories/vulnerability.repository.interface';
import { IncidentRepository } from '../../../domain/interfaces/repositories/incident.repository.interface';
import { CorrelationRepository } from '../../../domain/interfaces/repositories/correlation.repository.interface';

/**
 * Get Security Dashboard Query
 */
export class GetSecurityDashboardQuery {
  constructor(
    public readonly timeframe: 'last_24h' | 'last_7d' | 'last_30d' | 'last_90d' = 'last_24h',
    public readonly includeDetails: boolean = false
  ) {}
}

/**
 * Get Executive Dashboard Query
 */
export class GetExecutiveDashboardQuery {
  constructor(
    public readonly timeframe: 'last_7d' | 'last_30d' | 'last_90d' | 'last_year' = 'last_30d'
  ) {}
}

/**
 * Get SOC Dashboard Query
 */
export class GetSOCDashboardQuery {
  constructor(
    public readonly analystId?: string,
    public readonly shift: 'current' | 'previous' | 'all' = 'current'
  ) {}
}

/**
 * Get Threat Intelligence Dashboard Query
 */
export class GetThreatIntelligenceDashboardQuery {
  constructor(
    public readonly timeframe: 'last_24h' | 'last_7d' | 'last_30d' = 'last_7d'
  ) {}
}

/**
 * Security Dashboard Query Handler
 */
@QueryHandler(GetSecurityDashboardQuery)
export class GetSecurityDashboardQueryHandler implements IQueryHandler<GetSecurityDashboardQuery> {
  constructor(
    private readonly securityEventRepository: SecurityEventRepository,
    private readonly vulnerabilityRepository: VulnerabilityRepository,
    private readonly incidentRepository: IncidentRepository,
    private readonly correlationRepository: CorrelationRepository
  ) {}

  async execute(query: GetSecurityDashboardQuery): Promise<{
    summary: {
      totalEvents: number;
      totalVulnerabilities: number;
      totalIncidents: number;
      totalCorrelations: number;
      riskScore: number;
      securityPosture: 'excellent' | 'good' | 'fair' | 'poor';
    };
    events: {
      total: number;
      highRisk: number;
      processed: number;
      pending: number;
      recentTrends: Array<{ timestamp: string; count: number; riskScore: number }>;
    };
    vulnerabilities: {
      total: number;
      critical: number;
      high: number;
      activelyExploited: number;
      overdue: number;
      recentDiscoveries: number;
    };
    incidents: {
      total: number;
      active: number;
      critical: number;
      slaBreaches: number;
      averageResolutionTime: number;
      recentIncidents: Array<{
        id: string;
        title: string;
        severity: string;
        status: string;
        age: number;
      }>;
    };
    correlations: {
      total: number;
      highConfidence: number;
      attackChains: number;
      campaignAttributions: number;
      recentCorrelations: Array<{
        id: string;
        type: string;
        confidence: string;
        eventCount: number;
      }>;
    };
    threats: {
      activeCampaigns: number;
      newThreatActors: number;
      emergingThreats: number;
      topThreats: Array<{
        name: string;
        severity: string;
        confidence: string;
        lastSeen: string;
      }>;
    };
    performance: {
      eventProcessingRate: number;
      correlationAccuracy: number;
      incidentResponseTime: number;
      systemHealth: 'healthy' | 'degraded' | 'critical';
    };
    alerts: Array<{
      type: 'critical_vulnerability' | 'active_incident' | 'sla_breach' | 'system_issue';
      message: string;
      severity: 'low' | 'medium' | 'high' | 'critical';
      timestamp: string;
      actionRequired: boolean;
    }>;
  }> {
    try {
      const dateRange = this.getDateRange(query.timeframe);

      // Fetch data from all repositories in parallel
      const [
        eventStats,
        vulnerabilityStats,
        incidentStats,
        correlationStats,
      ] = await Promise.all([
        this.securityEventRepository.getStatistics({ dateRange }),
        this.vulnerabilityRepository.getStatistics(dateRange),
        this.incidentRepository.getStatistics(dateRange),
        this.correlationRepository.getStatistics(dateRange),
      ]);

      // Calculate overall risk score
      const riskScore = this.calculateOverallRiskScore(
        eventStats,
        vulnerabilityStats,
        incidentStats
      );

      // Determine security posture
      const securityPosture = this.determineSecurityPosture(riskScore, {
        eventStats,
        vulnerabilityStats,
        incidentStats,
      });

      // Get recent incidents for dashboard
      const recentIncidents = await this.getRecentIncidents();

      // Get recent correlations
      const recentCorrelations = await this.getRecentCorrelations();

      // Generate alerts
      const alerts = await this.generateSecurityAlerts(
        eventStats,
        vulnerabilityStats,
        incidentStats
      );

      return {
        summary: {
          totalEvents: eventStats.total,
          totalVulnerabilities: vulnerabilityStats.total,
          totalIncidents: incidentStats.total,
          totalCorrelations: correlationStats.total,
          riskScore,
          securityPosture,
        },
        events: {
          total: eventStats.total,
          highRisk: eventStats.requiresAttention || 0,
          processed: eventStats.total - eventStats.inProgress,
          pending: eventStats.inProgress || 0,
          recentTrends: [], // Will be populated from separate timeline query
        },
        vulnerabilities: {
          total: vulnerabilityStats.total,
          critical: vulnerabilityStats.riskLevelDistribution.critical || 0,
          high: vulnerabilityStats.riskLevelDistribution.high || 0,
          activelyExploited: vulnerabilityStats.exploitationMetrics.activelyExploited,
          overdue: vulnerabilityStats.remediationMetrics.overdueCount,
          recentDiscoveries: eventStats.createdLast24Hours || 0,
        },
        incidents: {
          total: incidentStats.total,
          active: Object.values(incidentStats.statusDistribution).reduce((sum, count, index) => 
            index < 6 ? sum + count : sum, 0), // Active statuses
          critical: incidentStats.priorityDistribution.critical || 0,
          slaBreaches: Math.round(incidentStats.total * (1 - incidentStats.responseMetrics.slaComplianceRate)),
          averageResolutionTime: incidentStats.responseMetrics.averageResolutionTime,
          recentIncidents,
        },
        correlations: {
          total: correlationStats.total,
          highConfidence: correlationStats.confidenceDistribution.high || 0,
          attackChains: correlationStats.patternAnalysis.attackChainRate * correlationStats.total,
          campaignAttributions: correlationStats.patternAnalysis.campaignAttributionRate * correlationStats.total,
          recentCorrelations,
        },
        threats: {
          activeCampaigns: 5, // Would be calculated from threat intelligence
          newThreatActors: 2,
          emergingThreats: 8,
          topThreats: [
            {
              name: 'APT29',
              severity: 'critical',
              confidence: 'high',
              lastSeen: new Date().toISOString(),
            },
          ],
        },
        performance: {
          eventProcessingRate: eventStats.processedLastHour || 0,
          correlationAccuracy: correlationStats.performanceMetrics.successRate || 0,
          incidentResponseTime: 0, // Will need to be calculated from incident data
          systemHealth: this.determineSystemHealth(eventStats, vulnerabilityStats, incidentStats),
        },
        alerts,
      };
    } catch (error) {
      throw new Error(`Failed to retrieve security dashboard: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private getDateRange(timeframe: string): { from: Date; to: Date } {
    const now = new Date();
    const from = new Date();

    switch (timeframe) {
      case 'last_24h':
        from.setHours(now.getHours() - 24);
        break;
      case 'last_7d':
        from.setDate(now.getDate() - 7);
        break;
      case 'last_30d':
        from.setDate(now.getDate() - 30);
        break;
      case 'last_90d':
        from.setDate(now.getDate() - 90);
        break;
    }

    return { from, to: now };
  }

  private calculateOverallRiskScore(eventStats: any, vulnerabilityStats: any, incidentStats: any): number {
    const eventRisk = eventStats.riskMetrics?.averageRiskScore || 50;
    const vulnerabilityRisk = vulnerabilityStats.averageRiskScore || 50;
    const incidentRisk = incidentStats.total > 0 ? 70 : 30; // Simple incident-based risk

    return Math.round((eventRisk * 0.3 + vulnerabilityRisk * 0.5 + incidentRisk * 0.2));
  }

  private determineSecurityPosture(riskScore: number, stats: any): 'excellent' | 'good' | 'fair' | 'poor' {
    if (riskScore >= 80) return 'poor';
    if (riskScore >= 60) return 'fair';
    if (riskScore >= 40) return 'good';
    return 'excellent';
  }

  private determineSystemHealth(eventStats: any, vulnerabilityStats: any, incidentStats: any): 'healthy' | 'degraded' | 'critical' {
    const errorRate = eventStats.processingMetrics?.errorRate || 0;
    const slaCompliance = incidentStats.responseMetrics?.slaComplianceRate || 1;

    if (errorRate > 0.1 || slaCompliance < 0.7) return 'critical';
    if (errorRate > 0.05 || slaCompliance < 0.9) return 'degraded';
    return 'healthy';
  }

  private async getRecentIncidents(): Promise<Array<{
    id: string;
    title: string;
    severity: string;
    status: string;
    age: number;
  }>> {
    try {
      const activeIncidents = await this.incidentRepository.findActiveIncidents();
      return activeIncidents.slice(0, 5).map(incident => ({
        id: incident.id.toString(),
        title: incident.title,
        severity: incident.severity,
        status: incident.status,
        age: incident.getAge(),
      }));
    } catch (error) {
      return [];
    }
  }

  private async getRecentCorrelations(): Promise<Array<{
    id: string;
    type: string;
    confidence: string;
    eventCount: number;
  }>> {
    try {
      const correlations = await this.correlationRepository.findHighConfidenceCorrelations();
      return correlations.slice(0, 5).map(correlation => ({
        id: (correlation as any).id?.toString() || 'unknown',
        type: (correlation as any).type || 'unknown',
        confidence: (correlation as any).confidence || 0,
        eventCount: (correlation as any).relatedEvents?.length || 0,
      }));
    } catch (error) {
      return [];
    }
  }

  private async generateSecurityAlerts(eventStats: any, vulnerabilityStats: any, incidentStats: any): Promise<Array<{
    type: 'critical_vulnerability' | 'active_incident' | 'sla_breach' | 'system_issue';
    message: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    timestamp: string;
    actionRequired: boolean;
  }>> {
    const alerts = [];
    const now = new Date().toISOString();

    // Critical vulnerabilities alert
    if (vulnerabilityStats.riskLevelDistribution?.critical > 0) {
      alerts.push({
        type: 'critical_vulnerability' as const,
        message: `${vulnerabilityStats.riskLevelDistribution.critical} critical vulnerabilities require immediate attention`,
        severity: 'critical' as const,
        timestamp: now,
        actionRequired: true,
      });
    }

    // Active incidents alert
    const activeIncidents = Object.values(incidentStats.statusDistribution).reduce((sum: number, count: number, index: number) => 
      index < 6 ? sum + count : sum, 0);
    if (activeIncidents > 0) {
      alerts.push({
        type: 'active_incident' as const,
        message: `${activeIncidents} active incidents in progress`,
        severity: activeIncidents > 5 ? 'high' : 'medium' as const,
        timestamp: now,
        actionRequired: true,
      });
    }

    // SLA breach alert
    const slaCompliance = incidentStats.responseMetrics?.slaComplianceRate || 1;
    if (slaCompliance < 0.9) {
      alerts.push({
        type: 'sla_breach' as const,
        message: `SLA compliance at ${Math.round(slaCompliance * 100)}% - below target`,
        severity: slaCompliance < 0.7 ? 'critical' : 'high' as const,
        timestamp: now,
        actionRequired: true,
      });
    }

    // System health alert
    const errorRate = eventStats.processingMetrics?.errorRate || 0;
    if (errorRate > 0.05) {
      alerts.push({
        type: 'system_issue' as const,
        message: `Event processing error rate at ${Math.round(errorRate * 100)}%`,
        severity: errorRate > 0.1 ? 'critical' : 'medium' as const,
        timestamp: now,
        actionRequired: true,
      });
    }

    return alerts;
  }
}

/**
 * Executive Dashboard Query Handler
 */
@QueryHandler(GetExecutiveDashboardQuery)
export class GetExecutiveDashboardQueryHandler implements IQueryHandler<GetExecutiveDashboardQuery> {
  constructor(
    private readonly securityEventRepository: SecurityEventRepository,
    private readonly vulnerabilityRepository: VulnerabilityRepository,
    private readonly incidentRepository: IncidentRepository
  ) {}

  async execute(query: GetExecutiveDashboardQuery): Promise<{
    riskOverview: {
      overallRiskScore: number;
      riskTrend: 'increasing' | 'stable' | 'decreasing';
      topRisks: Array<{
        category: string;
        risk: string;
        impact: 'low' | 'medium' | 'high' | 'critical';
        likelihood: 'low' | 'medium' | 'high';
        mitigation: string;
      }>;
    };
    businessImpact: {
      totalFinancialImpact: number;
      averageDowntime: number;
      customersAffected: number;
      reputationImpact: 'low' | 'medium' | 'high' | 'critical';
      complianceStatus: 'compliant' | 'at_risk' | 'non_compliant';
    };
    securityMetrics: {
      incidentResponseTime: number;
      vulnerabilityRemediationTime: number;
      threatDetectionRate: number;
      falsePositiveRate: number;
      securityPosture: 'excellent' | 'good' | 'fair' | 'poor';
    };
    trends: {
      incidents: Array<{ period: string; count: number; severity: number }>;
      vulnerabilities: Array<{ period: string; discovered: number; remediated: number }>;
      threats: Array<{ period: string; detected: number; blocked: number }>;
    };
    recommendations: Array<{
      priority: 'low' | 'medium' | 'high' | 'critical';
      category: string;
      recommendation: string;
      expectedImpact: string;
      estimatedCost: number;
      timeline: string;
    }>;
  }> {
    try {
      const dateRange = this.getDateRange(query.timeframe);

      const [incidentStats, vulnerabilityStats] = await Promise.all([
        this.incidentRepository.getStatistics(dateRange),
        this.vulnerabilityRepository.getStatistics(dateRange),
      ]);

      const businessImpact = incidentStats.businessImpact;
      const overallRiskScore = Math.round(
        (vulnerabilityStats.averageRiskScore * 0.6) + 
        (businessImpact.totalFinancialImpact > 100000 ? 80 : 40) * 0.4
      );

      return {
        riskOverview: {
          overallRiskScore,
          riskTrend: 'stable',
          topRisks: [
            {
              category: 'Cybersecurity',
              risk: 'Critical vulnerabilities in external-facing systems',
              impact: 'critical',
              likelihood: 'medium',
              mitigation: 'Accelerate patching program',
            },
          ],
        },
        businessImpact: {
          totalFinancialImpact: businessImpact.totalFinancialImpact,
          averageDowntime: businessImpact.averageDowntime,
          customersAffected: businessImpact.customersAffected,
          reputationImpact: businessImpact.reputationImpact > 3 ? 'high' : 'medium',
          complianceStatus: 'compliant',
        },
        securityMetrics: {
          incidentResponseTime: incidentStats.responseMetrics.averageResponseTime,
          vulnerabilityRemediationTime: vulnerabilityStats.remediationMetrics.averageRemediationTime,
          threatDetectionRate: 0.85,
          falsePositiveRate: 0.15,
          securityPosture: overallRiskScore < 40 ? 'excellent' : 
                          overallRiskScore < 60 ? 'good' : 
                          overallRiskScore < 80 ? 'fair' : 'poor',
        },
        trends: {
          incidents: incidentStats.trends.monthly,
          vulnerabilities: vulnerabilityStats.trends.monthly.map(trend => ({
            period: trend.month,
            discovered: trend.count,
            remediated: Math.round(trend.count * 0.7),
          })),
          threats: incidentStats.trends.monthly.map(trend => ({
            period: trend.month,
            detected: trend.count * 2,
            blocked: Math.round(trend.count * 1.8),
          })),
        },
        recommendations: [
          {
            priority: 'high',
            category: 'Vulnerability Management',
            recommendation: 'Implement automated vulnerability scanning',
            expectedImpact: 'Reduce vulnerability discovery time by 50%',
            estimatedCost: 150000,
            timeline: '3 months',
          },
        ],
      };
    } catch (error) {
      throw new Error(`Failed to retrieve executive dashboard: ${error.message}`);
    }
  }

  private getDateRange(timeframe: string): { from: Date; to: Date } {
    const now = new Date();
    const from = new Date();

    switch (timeframe) {
      case 'last_7d':
        from.setDate(now.getDate() - 7);
        break;
      case 'last_30d':
        from.setDate(now.getDate() - 30);
        break;
      case 'last_90d':
        from.setDate(now.getDate() - 90);
        break;
      case 'last_year':
        from.setFullYear(now.getFullYear() - 1);
        break;
    }

    return { from, to: now };
  }
}

/**
 * Dashboard Query Handlers Export
 */
export const DashboardQueryHandlers = [
  GetSecurityDashboardQueryHandler,
  GetExecutiveDashboardQueryHandler,
];

/**
 * Dashboard Queries Export
 */
export const DashboardQueries = [
  GetSecurityDashboardQuery,
  GetExecutiveDashboardQuery,
  GetSOCDashboardQuery,
  GetThreatIntelligenceDashboardQuery,
];
