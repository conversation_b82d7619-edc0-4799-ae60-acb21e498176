import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { EventBus } from '@nestjs/cqrs';
import { Queue } from 'bull';
import { getQueueToken } from '@nestjs/bull';
import { PipelineManagerService } from '../pipeline-manager.service';

describe('PipelineManagerService', () => {
  let service: PipelineManagerService;
  let mockAiRequestQueue: jest.Mocked<Queue>;
  let mockAiResponseQueue: jest.Mocked<Queue>;
  let mockTrainingQueue: jest.Mocked<Queue>;
  let mockEventBus: jest.Mocked<EventBus>;
  let mockConfigService: jest.Mocked<ConfigService>;

  const mockPipelineDefinition = {
    name: 'Test Pipeline',
    description: 'Test pipeline for unit tests',
    stages: [
      {
        id: 'stage-1',
        name: 'Data Preprocessing',
        type: 'data-preprocessing',
        config: { normalize: true },
      },
      {
        id: 'stage-2',
        name: 'AI Analysis',
        type: 'ai-analysis',
        dependencies: ['stage-1'],
        config: { model: 'test-model' },
      },
    ],
    executionStrategy: 'sequential' as const,
    context: { testData: 'test' },
  };

  beforeEach(async () => {
    // Create mocks
    mockAiRequestQueue = {
      add: jest.fn(),
      process: jest.fn(),
    } as any;

    mockAiResponseQueue = {
      add: jest.fn(),
      process: jest.fn(),
    } as any;

    mockTrainingQueue = {
      add: jest.fn(),
      process: jest.fn(),
    } as any;

    mockEventBus = {
      publish: jest.fn(),
    } as any;

    mockConfigService = {
      get: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PipelineManagerService,
        {
          provide: getQueueToken('ai-request'),
          useValue: mockAiRequestQueue,
        },
        {
          provide: getQueueToken('ai-response'),
          useValue: mockAiResponseQueue,
        },
        {
          provide: getQueueToken('training-job'),
          useValue: mockTrainingQueue,
        },
        {
          provide: EventBus,
          useValue: mockEventBus,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<PipelineManagerService>(PipelineManagerService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createPipeline', () => {
    it('should create and execute pipeline successfully', async () => {
      // Arrange
      const mockJob = {
        finished: jest.fn().mockResolvedValue({ result: 'success' }),
      };
      mockAiRequestQueue.add.mockResolvedValue(mockJob as any);

      // Act
      const execution = await service.createPipeline(mockPipelineDefinition);

      // Assert
      expect(execution).toBeDefined();
      expect(execution.id).toMatch(/^pipeline-\d+-[a-z0-9]+$/);
      expect(execution.definition).toEqual(mockPipelineDefinition);
      expect(execution.stages).toHaveLength(2);
      expect(execution.status).toBe('completed');
    });

    it('should validate pipeline definition', async () => {
      // Arrange
      const invalidDefinition = {
        ...mockPipelineDefinition,
        stages: [], // Empty stages
      };

      // Act & Assert
      await expect(service.createPipeline(invalidDefinition))
        .rejects.toThrow('Pipeline must have at least one stage');
    });

    it('should validate stage dependencies', async () => {
      // Arrange
      const invalidDefinition = {
        ...mockPipelineDefinition,
        stages: [
          {
            id: 'stage-1',
            name: 'Test Stage',
            type: 'ai-analysis',
            dependencies: ['non-existent-stage'], // Invalid dependency
          },
        ],
      };

      // Act & Assert
      await expect(service.createPipeline(invalidDefinition))
        .rejects.toThrow('Invalid dependency: non-existent-stage for stage: stage-1');
    });

    it('should handle pipeline execution failures', async () => {
      // Arrange
      const mockJob = {
        finished: jest.fn().mockRejectedValue(new Error('Job failed')),
      };
      mockAiRequestQueue.add.mockResolvedValue(mockJob as any);

      // Act & Assert
      await expect(service.createPipeline(mockPipelineDefinition))
        .rejects.toThrow('Pipeline creation failed');
    });
  });

  describe('executePipelineFromTemplate', () => {
    beforeEach(() => {
      // Register a test template
      service.registerPipelineTemplate('test-template', {
        name: 'Test Template',
        description: 'Template for testing',
        stages: [
          {
            id: 'template-stage',
            name: 'Template Stage',
            type: 'ai-analysis',
            config: { model: 'template-model' },
          },
        ],
        executionStrategy: 'sequential',
      });
    });

    it('should execute pipeline from template successfully', async () => {
      // Arrange
      const parameters = { testParam: 'value' };
      const mockJob = {
        finished: jest.fn().mockResolvedValue({ result: 'success' }),
      };
      mockAiRequestQueue.add.mockResolvedValue(mockJob as any);

      // Act
      const execution = await service.executePipelineFromTemplate('test-template', parameters);

      // Assert
      expect(execution).toBeDefined();
      expect(execution.definition.name).toBe('Test Template');
      expect(execution.context).toEqual(parameters);
    });

    it('should throw error for non-existent template', async () => {
      // Act & Assert
      await expect(service.executePipelineFromTemplate('non-existent', {}))
        .rejects.toThrow('Pipeline template not found: non-existent');
    });
  });

  describe('getPipelineStatus', () => {
    it('should return pipeline status correctly', async () => {
      // Arrange
      const mockJob = {
        finished: jest.fn().mockResolvedValue({ result: 'success' }),
      };
      mockAiRequestQueue.add.mockResolvedValue(mockJob as any);

      const execution = await service.createPipeline(mockPipelineDefinition);

      // Act
      const status = await service.getPipelineStatus(execution.id);

      // Assert
      expect(status).toEqual({
        id: execution.id,
        status: 'completed',
        progress: 1, // 100% complete
        currentStage: null, // No stage currently running
        metrics: expect.objectContaining({
          startTime: expect.any(Date),
          endTime: expect.any(Date),
          totalStages: 2,
          completedStages: 2,
          failedStages: 0,
        }),
        results: expect.any(Object),
        errors: [],
      });
    });

    it('should throw error for non-existent pipeline', async () => {
      // Act & Assert
      await expect(service.getPipelineStatus('non-existent'))
        .rejects.toThrow('Pipeline not found: non-existent');
    });
  });

  describe('cancelPipeline', () => {
    it('should cancel running pipeline successfully', async () => {
      // Arrange
      const mockJob = {
        finished: jest.fn().mockImplementation(() => 
          new Promise(resolve => setTimeout(resolve, 1000)) // Long running job
        ),
      };
      mockAiRequestQueue.add.mockResolvedValue(mockJob as any);

      // Start pipeline but don't wait for completion
      const executionPromise = service.createPipeline(mockPipelineDefinition);
      
      // Wait a bit to ensure pipeline starts
      await new Promise(resolve => setTimeout(resolve, 100));

      // Act
      const execution = await executionPromise;
      await service.cancelPipeline(execution.id);

      // Assert
      const status = await service.getPipelineStatus(execution.id);
      expect(status.status).toBe('cancelled');
    });

    it('should throw error for non-existent pipeline', async () => {
      // Act & Assert
      await expect(service.cancelPipeline('non-existent'))
        .rejects.toThrow('Pipeline not found: non-existent');
    });
  });

  describe('retryStage', () => {
    it('should retry failed stage successfully', async () => {
      // Arrange
      const failingJob = {
        finished: jest.fn().mockRejectedValue(new Error('Stage failed')),
      };
      const successJob = {
        finished: jest.fn().mockResolvedValue({ result: 'success' }),
      };

      mockAiRequestQueue.add
        .mockResolvedValueOnce(failingJob as any)
        .mockResolvedValueOnce(successJob as any);

      // Create pipeline that will fail
      let execution;
      try {
        execution = await service.createPipeline(mockPipelineDefinition);
      } catch (error) {
        // Pipeline should fail, but we can still get the execution
        const activePipelines = await service.getActivePipelines();
        execution = activePipelines[0];
      }

      // Act
      await service.retryStage(execution.id, 'stage-2');

      // Assert
      const status = await service.getPipelineStatus(execution.id);
      expect(status.status).toBe('completed');
    });

    it('should throw error for non-failed stage', async () => {
      // Arrange
      const mockJob = {
        finished: jest.fn().mockResolvedValue({ result: 'success' }),
      };
      mockAiRequestQueue.add.mockResolvedValue(mockJob as any);

      const execution = await service.createPipeline(mockPipelineDefinition);

      // Act & Assert
      await expect(service.retryStage(execution.id, 'stage-1'))
        .rejects.toThrow('Stage is not in failed state: stage-1');
    });
  });

  describe('getActivePipelines', () => {
    it('should return all active pipelines', async () => {
      // Arrange
      const mockJob = {
        finished: jest.fn().mockResolvedValue({ result: 'success' }),
      };
      mockAiRequestQueue.add.mockResolvedValue(mockJob as any);

      const execution1 = await service.createPipeline(mockPipelineDefinition);
      const execution2 = await service.createPipeline({
        ...mockPipelineDefinition,
        name: 'Second Pipeline',
      });

      // Act
      const activePipelines = await service.getActivePipelines();

      // Assert
      expect(activePipelines).toHaveLength(2);
      expect(activePipelines.map(p => p.id)).toContain(execution1.id);
      expect(activePipelines.map(p => p.id)).toContain(execution2.id);
    });
  });

  describe('stage execution', () => {
    it('should execute AI analysis stage', async () => {
      // Arrange
      const mockJob = {
        finished: jest.fn().mockResolvedValue({ 
          analysis: 'threat detected',
          confidence: 0.95 
        }),
      };
      mockAiRequestQueue.add.mockResolvedValue(mockJob as any);

      const aiAnalysisDefinition = {
        ...mockPipelineDefinition,
        stages: [
          {
            id: 'ai-stage',
            name: 'AI Analysis',
            type: 'ai-analysis',
            config: { model: 'threat-detection' },
          },
        ],
      };

      // Act
      const execution = await service.createPipeline(aiAnalysisDefinition);

      // Assert
      expect(mockAiRequestQueue.add).toHaveBeenCalledWith('analyze', {
        pipelineId: execution.id,
        stageId: 'ai-stage',
        data: expect.any(Object),
        config: { model: 'threat-detection' },
      });
      expect(execution.results['ai-stage']).toEqual({
        analysis: 'threat detected',
        confidence: 0.95,
      });
    });

    it('should execute model training stage', async () => {
      // Arrange
      const mockJob = {
        finished: jest.fn().mockResolvedValue({ 
          modelId: 'trained-model-123',
          metrics: { accuracy: 0.95 }
        }),
      };
      mockTrainingQueue.add.mockResolvedValue(mockJob as any);

      const trainingDefinition = {
        ...mockPipelineDefinition,
        stages: [
          {
            id: 'training-stage',
            name: 'Model Training',
            type: 'model-training',
            config: { epochs: 10 },
          },
        ],
      };

      // Act
      const execution = await service.createPipeline(trainingDefinition);

      // Assert
      expect(mockTrainingQueue.add).toHaveBeenCalledWith('train', {
        pipelineId: execution.id,
        stageId: 'training-stage',
        data: expect.any(Object),
        config: { epochs: 10 },
      });
      expect(execution.results['training-stage']).toEqual({
        modelId: 'trained-model-123',
        metrics: { accuracy: 0.95 },
      });
    });

    it('should execute data preprocessing stage', async () => {
      // Arrange
      const preprocessingDefinition = {
        ...mockPipelineDefinition,
        stages: [
          {
            id: 'preprocess-stage',
            name: 'Data Preprocessing',
            type: 'data-preprocessing',
            config: { normalize: true, validate: true },
          },
        ],
      };

      // Act
      const execution = await service.createPipeline(preprocessingDefinition);

      // Assert
      expect(execution.results['preprocess-stage']).toEqual({
        processedData: expect.any(Object),
        metadata: { stage: 'preprocess-stage' },
      });
    });

    it('should execute conditional stage', async () => {
      // Arrange
      const conditionalDefinition = {
        ...mockPipelineDefinition,
        stages: [
          {
            id: 'conditional-stage',
            name: 'Conditional Logic',
            type: 'conditional',
            config: { condition: 'always_true' },
          },
        ],
      };

      // Act
      const execution = await service.createPipeline(conditionalDefinition);

      // Assert
      expect(execution.results['conditional-stage']).toEqual({
        conditionMet: true,
        result: 'proceed',
      });
    });

    it('should execute parallel batch stage', async () => {
      // Arrange
      const batchDefinition = {
        ...mockPipelineDefinition,
        stages: [
          {
            id: 'batch-stage',
            name: 'Parallel Batch',
            type: 'parallel-batch',
            config: { batchSize: 5 },
            input: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], // 10 items
          },
        ],
      };

      // Act
      const execution = await service.createPipeline(batchDefinition);

      // Assert
      expect(execution.results['batch-stage']).toHaveLength(10);
      expect(execution.results['batch-stage'][0]).toEqual({
        ...1,
        processed: true,
      });
    });
  });

  describe('parallel execution', () => {
    it('should execute stages in parallel when specified', async () => {
      // Arrange
      const mockJob = {
        finished: jest.fn().mockResolvedValue({ result: 'success' }),
      };
      mockAiRequestQueue.add.mockResolvedValue(mockJob as any);

      const parallelDefinition = {
        ...mockPipelineDefinition,
        executionStrategy: 'parallel' as const,
        stages: [
          {
            id: 'parallel-stage-1',
            name: 'Parallel Stage 1',
            type: 'data-preprocessing',
            config: {},
          },
          {
            id: 'parallel-stage-2',
            name: 'Parallel Stage 2',
            type: 'data-preprocessing',
            config: {},
          },
        ],
      };

      // Act
      const execution = await service.createPipeline(parallelDefinition);

      // Assert
      expect(execution.status).toBe('completed');
      expect(execution.metrics.completedStages).toBe(2);
    });

    it('should handle stage dependencies in parallel execution', async () => {
      // Arrange
      const mockJob = {
        finished: jest.fn().mockResolvedValue({ result: 'success' }),
      };
      mockAiRequestQueue.add.mockResolvedValue(mockJob as any);

      const dependentParallelDefinition = {
        ...mockPipelineDefinition,
        executionStrategy: 'parallel' as const,
        stages: [
          {
            id: 'independent-stage',
            name: 'Independent Stage',
            type: 'data-preprocessing',
            config: {},
          },
          {
            id: 'dependent-stage',
            name: 'Dependent Stage',
            type: 'ai-analysis',
            dependencies: ['independent-stage'],
            config: {},
          },
        ],
      };

      // Act
      const execution = await service.createPipeline(dependentParallelDefinition);

      // Assert
      expect(execution.status).toBe('completed');
      expect(execution.metrics.completedStages).toBe(2);
    });
  });

  describe('error handling', () => {
    it('should handle stage failures with continueOnFailure', async () => {
      // Arrange
      const failingJob = {
        finished: jest.fn().mockRejectedValue(new Error('Stage failed')),
      };
      const successJob = {
        finished: jest.fn().mockResolvedValue({ result: 'success' }),
      };

      mockAiRequestQueue.add
        .mockResolvedValueOnce(failingJob as any)
        .mockResolvedValueOnce(successJob as any);

      const resilientDefinition = {
        ...mockPipelineDefinition,
        stages: [
          {
            id: 'failing-stage',
            name: 'Failing Stage',
            type: 'ai-analysis',
            continueOnFailure: true,
            config: {},
          },
          {
            id: 'success-stage',
            name: 'Success Stage',
            type: 'data-preprocessing',
            config: {},
          },
        ],
      };

      // Act
      const execution = await service.createPipeline(resilientDefinition);

      // Assert
      expect(execution.status).toBe('completed');
      expect(execution.metrics.failedStages).toBe(1);
      expect(execution.metrics.completedStages).toBe(1);
    });

    it('should fail pipeline when critical stage fails', async () => {
      // Arrange
      const failingJob = {
        finished: jest.fn().mockRejectedValue(new Error('Critical stage failed')),
      };
      mockAiRequestQueue.add.mockResolvedValue(failingJob as any);

      const criticalDefinition = {
        ...mockPipelineDefinition,
        stages: [
          {
            id: 'critical-stage',
            name: 'Critical Stage',
            type: 'ai-analysis',
            continueOnFailure: false, // Critical stage
            config: {},
          },
        ],
      };

      // Act & Assert
      await expect(service.createPipeline(criticalDefinition))
        .rejects.toThrow('Pipeline creation failed');
    });
  });

  describe('template management', () => {
    it('should register pipeline template successfully', () => {
      // Arrange
      const template = {
        name: 'Custom Template',
        description: 'Custom pipeline template',
        stages: [
          {
            id: 'custom-stage',
            name: 'Custom Stage',
            type: 'ai-analysis',
            config: { model: 'custom-model' },
          },
        ],
        executionStrategy: 'sequential' as const,
      };

      // Act
      service.registerPipelineTemplate('custom-template', template);

      // Assert - Should not throw error and template should be usable
      expect(() => service.registerPipelineTemplate('custom-template', template))
        .not.toThrow();
    });
  });

  describe('pipeline metrics and monitoring', () => {
    it('should track pipeline metrics correctly', async () => {
      // Arrange
      const mockJob = {
        finished: jest.fn().mockResolvedValue({ result: 'success' }),
      };
      mockAiRequestQueue.add.mockResolvedValue(mockJob as any);

      // Act
      const execution = await service.createPipeline(mockPipelineDefinition);

      // Assert
      expect(execution.metrics).toEqual({
        startTime: expect.any(Date),
        endTime: expect.any(Date),
        totalStages: 2,
        completedStages: 2,
        failedStages: 0,
      });
      expect(execution.metrics.endTime.getTime()).toBeGreaterThan(
        execution.metrics.startTime.getTime()
      );
    });

    it('should calculate progress correctly', async () => {
      // Arrange
      const mockJob = {
        finished: jest.fn().mockResolvedValue({ result: 'success' }),
      };
      mockAiRequestQueue.add.mockResolvedValue(mockJob as any);

      const execution = await service.createPipeline(mockPipelineDefinition);

      // Act
      const status = await service.getPipelineStatus(execution.id);

      // Assert
      expect(status.progress).toBe(1); // 100% complete (2/2 stages)
    });
  });
});