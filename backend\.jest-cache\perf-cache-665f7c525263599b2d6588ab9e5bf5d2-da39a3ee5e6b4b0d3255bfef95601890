{"C:\\Users\\<USER>\\sentinel\\backend\\src\\api\\validation\\validators\\__tests__\\is-severity-level.validator.spec.ts": [1, 109], "C:\\Users\\<USER>\\sentinel\\backend\\src\\api\\validation\\validators\\__tests__\\is-cve-id.validator.spec.ts": [1, 104], "C:\\Users\\<USER>\\sentinel\\backend\\src\\api\\validation\\validators\\__tests__\\is-ip-address.validator.spec.ts": [1, 114], "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\security\\headers\\__tests__\\hsts.config.spec.ts": [1, 185], "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\security\\headers\\__tests__\\csp.config.spec.ts": [1, 178], "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\security\\headers\\__tests__\\cors.config.spec.ts": [1, 158], "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\security\\headers\\__tests__\\security-headers.middleware.spec.ts": [1, 180], "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\alerting\\tests\\integration\\cross-module.integration.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\alerting\\tests\\integration\\workflow-execution.integration.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\alerting\\tests\\integration\\dashboard-analytics.integration.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\alerting\\tests\\integration\\performance-load.integration.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\alerting\\tests\\integration\\notification-provider.integration.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\testing\\integration\\monitoring-infrastructure.integration.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\testing\\integration\\workflow-execution.integration.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\compliance-audit\\application\\services\\compliance-monitoring.service.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\testing\\integration\\api-endpoints.integration.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\testing\\integration\\cross-module.integration.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\asset-management\\application\\services\\asset-inventory.service.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\testing\\integration\\background-processors.integration.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\security\\__tests__\\secrets.service.spec.ts": [0, 988], "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\auth\\__tests__\\rbac.service.spec.ts": [0, 500], "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\tests\\performance\\load-testing.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai-ml\\application\\services\\vulnerability-analysis.service.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\api\\controllers\\__tests__\\vulnerability-assessment.controller.integration.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\incident-response\\application\\services\\incident-management.service.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\compliance-audit\\testing\\compliance-framework.service.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\threat-intelligence\\api\\controllers\\__tests__\\threat-intelligence.controller.integration.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\security\\__tests__\\encryption.service.spec.ts": [1, 276], "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\tests\\integration\\database-integration.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\threat-intelligence\\application\\services\\__tests__\\threat-intelligence.service.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\application\\services\\threat-hunting.service.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\auth\\__tests__\\session.store.spec.ts": [0, 430], "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\threat-intelligence\\infrastructure\\jobs\\__tests__\\threat-analysis.processor.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\application\\services\\vulnerability-assessment.service.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\config\\__tests__\\configuration.service.spec.ts": [0, 192], "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\config\\__tests__\\environment.validator.spec.ts": [0, 313], "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting-analytics\\application\\services\\report-generation.service.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\entities\\threat\\threat.entity.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\api\\controllers\\__tests__\\vulnerability.controller.integration.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\orchestration\\__tests__\\ai-orchestration.service.spec.ts": [1, 912], "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\application\\services\\asset-management.service.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\tests\\unit\\controllers\\report-definition.controller.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\config\\__tests__\\database.config.spec.ts": [0, 207], "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\application\\services\\__tests__\\vulnerability-feed.service.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\threat-intelligence\\application\\services\\ioc.service.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\tests\\unit\\services\\report-definition.service.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\application\\services\\__tests__\\vulnerability-reporting.service.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\application\\services\\__tests__\\vulnerability-assessment.service.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\application\\services\\__tests__\\vulnerability-scan.service.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\entities\\event\\security-event.entity.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\application\\services\\__tests__\\vulnerability.service.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\user-management\\application\\services\\user.service.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\value-objects\\network\\ip-address.value-object.spec.ts": [1, 80], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\application\\services\\correlation.service.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\application\\services\\vulnerability.service.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\logging\\__tests__\\logging-integration.test.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\app.controller.spec.ts": [0, 155], "C:\\Users\\<USER>\\sentinel\\backend\\src\\__tests__\\e2e\\integration-workflow.e2e-spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\__tests__\\e2e\\error-scenarios.e2e-spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\__tests__\\e2e\\security-workflow.e2e-spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\__tests__\\e2e\\health-monitoring-workflow.e2e-spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\api\\__tests__\\error-handling-integration.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\__tests__\\e2e\\api-workflow.e2e-spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\domain\\base-service.spec.ts": [1, 378], "C:\\Users\\<USER>\\sentinel\\backend\\src\\api\\__tests__\\api-integration.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\__tests__\\e2e\\authentication-workflow.e2e-spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\api\\__tests__\\validation-integration.spec.ts": [0, 680], "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\domain\\base-aggregate-root.spec.ts": [1, 73], "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\domain\\base-domain-event.spec.ts": [1, 88], "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\domain\\base-specification.spec.ts": [1, 77], "C:\\Users\\<USER>\\sentinel\\backend\\src\\api\\__tests__\\rate-limiting-integration.spec.ts": [0, 257], "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\value-objects\\unique-entity-id.spec.ts": [1, 102], "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\domain\\base-entity.spec.ts": [1, 70], "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\value-objects\\base-value-object.spec.ts": [1, 68], "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\value-objects\\version.spec.ts": [0, 191], "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\value-objects\\correlation-id.spec.ts": [0, 132], "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\value-objects\\timestamp.spec.ts": [0, 122], "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\value-objects\\user-id.spec.ts": [0, 103], "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\value-objects\\tenant-id.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\exceptions\\service-unavailable.exception.spec.ts": [0, 103], "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\exceptions\\rate-limit.exception.spec.ts": [1, 80], "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\exceptions\\conflict.exception.spec.ts": [1, 72], "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\exceptions\\forbidden.exception.spec.ts": [1, 72], "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\exceptions\\index.spec.ts": [0, 78], "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\exceptions\\unauthorized.exception.spec.ts": [1, 60], "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\utils\\validation.utils.spec.ts": [0, 91], "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\utils\\serialization.utils.spec.ts": [0, 95], "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\types\\security.types.spec.ts": [0, 94], "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\utils\\crypto.utils.spec.ts": [1, 500], "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\types\\audit.types.spec.ts": [0, 74], "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\types\\response.types.spec.ts": [1, 58], "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\types\\sort.types.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\patterns\\circuit-breaker.spec.ts": [1, 707], "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\patterns\\cache-strategy.spec.ts": [0, 586], "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\patterns\\retry-strategy.spec.ts": [1, 680], "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\decorators\\rate-limit.decorator.spec.ts": [1, 214], "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\decorators\\retry.decorator.spec.ts": [1, 1531], "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\decorators\\cache.decorator.spec.ts": [1, 123], "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\decorators\\audit.decorator.spec.ts": [1, 171], "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\decorators\\index.spec.ts": [1, 1798], "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\patterns\\index.spec.ts": [1, 1939], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\enums\\__tests__\\enrichment-source.enum.spec.ts": [0, 74], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\enums\\__tests__\\action-status.enum.spec.ts": [1, 62], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\enums\\__tests__\\action-type.enum.spec.ts": [0, 59], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\enums\\__tests__\\vulnerability-severity.enum.spec.ts": [1, 58], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\value-objects\\event-metadata\\__tests__\\event-metadata.value-object.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\value-objects\\event-metadata\\__tests__\\event-source.value-object.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\value-objects\\event-metadata\\__tests__\\event-timestamp.value-object.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\value-objects\\threat-indicators\\__tests__\\threat-signature.value-object.spec.ts": [1, 77], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\value-objects\\threat-indicators\\__tests__\\cvss-score.value-object.spec.ts": [1, 81], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\value-objects\\threat-indicators\\__tests__\\ioc.value-object.spec.ts": [1, 80], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\value-objects\\network\\network-segment.value-object.spec.ts": [1, 100], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\value-objects\\network\\port.value-object.spec.ts": [1, 87], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\specifications\\__tests__\\event.specification.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\entities\\__tests__\\event.entity.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\factories\\__tests__\\event.factory.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\specifications\\__tests__\\normalized-event.specification.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\entities\\__tests__\\normalized-event.entity.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\factories\\__tests__\\normalized-event.factory.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\specifications\\__tests__\\correlated-event.specification.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\specifications\\__tests__\\enriched-event.specification.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\entities\\__tests__\\enriched-event.entity.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\__tests__\\correlated-event-correlation-failed.domain-event.spec.ts": [0, 240], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\__tests__\\correlated-event-status-changed.domain-event.spec.ts": [0, 218], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\__tests__\\correlated-event-created.domain-event.spec.ts": [0, 205], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\factories\\__tests__\\correlated-event.factory.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\factories\\__tests__\\enriched-event.factory.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\__tests__\\enriched-event-created.domain-event.spec.ts": [0, 161], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\entities\\__tests__\\correlated-event.entity.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\specifications\\__tests__\\threat.specification.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\factories\\__tests__\\threat.factory.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\specifications\\__tests__\\vulnerability.specification.spec.ts": [0, 179], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\__tests__\\vulnerability-status-changed.event.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\factories\\__tests__\\vulnerability.factory.spec.ts": [1, 116], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\specifications\\__tests__\\response-action.specification.spec.ts": [0, 272], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\entities\\__tests__\\response-action.entity.spec.ts": [1, 182], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\factories\\__tests__\\response-action.factory.spec.ts": [1, 158], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\__tests__\\response-action-failed.domain-event.spec.ts": [1, 170], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\__tests__\\response-action-rolled-back.domain-event.spec.ts": [0, 168], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\__tests__\\response-action-status-changed.domain-event.spec.ts": [1, 152], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\__tests__\\response-action-executed.domain-event.spec.ts": [1, 150], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\__tests__\\response-action-created.domain-event.spec.ts": [1, 137], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\interfaces\\data\\__tests__\\vulnerability-data.interface.spec.ts": [1, 82], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\interfaces\\data\\__tests__\\threat-data.interface.spec.ts": [1, 67], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\interfaces\\data\\__tests__\\security-event-data.interface.spec.ts": [1, 66], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\__tests__\\threat-detected.domain-event.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\__tests__\\recommendation-generated.domain-event.spec.ts": [1, 146], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\__tests__\\event-received.domain-event.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\application\\services\\__tests__\\security-orchestrator.service.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\policies\\__tests__\\security-policy.spec.ts": [1, 86], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\policies\\__tests__\\policy-audit-trail.spec.ts": [1, 85], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\policies\\__tests__\\compliance-policy.spec.ts": [1, 80], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\policies\\__tests__\\policy-configuration.spec.ts": [1, 86], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\__tests__\\integration\\application-services.integration.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\__tests__\\integration\\integration-test-runner.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\__tests__\\integration\\error-handling.integration.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\__tests__\\integration\\repository-contracts.integration.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\__tests__\\integration\\domain-events.integration.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\__tests__\\integration\\security-workflow.integration.spec.ts": [1, 0], "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\patterns\\optimized-specification.spec.ts": [0, 403], "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\patterns\\domain-event-batch.spec.ts": [0, 17482], "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\patterns\\performance-monitor.spec.ts": [0, 61444], "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\patterns\\value-object-cache.spec.ts": [0, 104], "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\aggregates\\__tests__\\ai-model.aggregate.spec.ts": [0, 99], "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\factories\\__tests__\\ai-model.factory.spec.ts": [1, 93], "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\entities\\__tests__\\ai-model.entity.spec.ts": [1, 89], "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\value-objects\\__tests__\\prediction-result.value-object.spec.ts": [0, 342], "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\value-objects\\__tests__\\precision-recall.value-object.spec.ts": [0, 239], "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\value-objects\\__tests__\\confidence-score.value-object.spec.ts": [0, 129], "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\value-objects\\__tests__\\accuracy-score.value-object.spec.ts": [0, 138], "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\orchestration\\__tests__\\model-selection.service.spec.ts": [1, 813], "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\orchestration\\__tests__\\pipeline-manager.service.spec.ts": [1, 1982]}