import { BaseIntegrationEvent } from '../../../../../shared-kernel/integration/base-integration-event';
import { ThreatSeverity } from '../../../domain/enums/threat-severity.enum';

/**
 * SIEM Integration Event Data
 */
export interface SIEMIntegrationEventData {
  /** Event type identifier */
  eventType: 'alert_created' | 'alert_updated' | 'alert_closed' | 'correlation_found' | 'dashboard_update';
  /** SIEM platform information */
  siemPlatform: {
    /** Platform name (Splunk, QRadar, Sentinel, etc.) */
    platform: string;
    /** Platform version */
    version: string;
    /** Instance identifier */
    instanceId: string;
    /** API endpoint */
    endpoint: string;
  };
  /** Alert/Event data */
  alertData: {
    /** SIEM alert ID */
    siemAlertId: string;
    /** Internal event ID */
    internalEventId?: string;
    /** Alert title */
    title: string;
    /** Alert description */
    description: string;
    /** Alert severity */
    severity: ThreatSeverity;
    /** Alert status */
    status: 'open' | 'investigating' | 'closed' | 'false_positive';
    /** Alert category */
    category: string;
    /** Alert type */
    type: string;
    /** Source IP addresses */
    sourceIPs: string[];
    /** Destination IP addresses */
    destinationIPs: string[];
    /** Affected hosts */
    affectedHosts: string[];
    /** User accounts involved */
    userAccounts: string[];
    /** Detection rule */
    detectionRule: {
      ruleId: string;
      ruleName: string;
      ruleType: string;
      confidence: number;
    };
    /** Event timestamps */
    timestamps: {
      firstSeen: string;
      lastSeen: string;
      createdAt: string;
      updatedAt: string;
    };
    /** Raw event data */
    rawData: Record<string, any>;
    /** Event count */
    eventCount: number;
    /** Risk score */
    riskScore: number;
  };
  /** Correlation information */
  correlation?: {
    /** Related alerts */
    relatedAlerts: string[];
    /** Correlation rules triggered */
    correlationRules: string[];
    /** Correlation confidence */
    confidence: number;
    /** Attack timeline */
    attackTimeline: Array<{
      timestamp: string;
      event: string;
      description: string;
    }>;
  };
  /** Integration metadata */
  integration: {
    /** Integration ID */
    integrationId: string;
    /** Sync direction */
    direction: 'inbound' | 'outbound' | 'bidirectional';
    /** Processing timestamp */
    processedAt: string;
    /** Processing duration (ms) */
    processingDuration: number;
    /** Mapping applied */
    mappingApplied: string[];
    /** Enrichment applied */
    enrichmentApplied: string[];
    /** Sync status */
    syncStatus: 'success' | 'partial' | 'failed';
    /** Error details */
    errors: string[];
  };
}

/**
 * SIEM Integration Event
 * 
 * Published when security events are synchronized with SIEM platforms
 * for centralized monitoring and correlation.
 * 
 * Key use cases:
 * - Synchronize alerts between SIEM and security platform
 * - Maintain bidirectional data flow for correlation
 * - Update alert statuses across platforms
 * - Share enrichment data and threat intelligence
 * - Coordinate incident response activities
 * - Provide unified security operations view
 */
export class SIEMIntegrationEvent extends BaseIntegrationEvent<SIEMIntegrationEventData> {
  constructor(
    eventData: SIEMIntegrationEventData,
    options?: {
      eventId?: string;
      correlationId?: string;
      causationId?: string;
      timestamp?: Date;
      version?: string;
      metadata?: Record<string, any>;
    }
  ) {
    super('SIEMIntegration', eventData, {
      version: '1.0',
      ...options,
      metadata: {
        domain: 'Security',
        category: 'Integration',
        ...options?.metadata,
      },
    });
  }

  /**
   * Get SIEM alert ID
   */
  get siemAlertId(): string {
    return this.eventData.alertData.siemAlertId;
  }

  /**
   * Get internal event ID
   */
  get internalEventId(): string | undefined {
    return this.eventData.alertData.internalEventId;
  }

  /**
   * Get SIEM platform
   */
  get siemPlatform(): string {
    return this.eventData.siemPlatform.platform;
  }

  /**
   * Get alert severity
   */
  get severity(): ThreatSeverity {
    return this.eventData.alertData.severity;
  }

  /**
   * Get alert status
   */
  get status(): 'open' | 'investigating' | 'closed' | 'false_positive' {
    return this.eventData.alertData.status;
  }

  /**
   * Get risk score
   */
  get riskScore(): number {
    return this.eventData.alertData.riskScore;
  }

  /**
   * Get sync direction
   */
  get syncDirection(): 'inbound' | 'outbound' | 'bidirectional' {
    return this.eventData.integration.direction;
  }

  /**
   * Check if alert is high severity
   */
  isHighSeverity(): boolean {
    return this.eventData.alertData.severity === ThreatSeverity.HIGH ||
           this.eventData.alertData.severity === ThreatSeverity.CRITICAL;
  }

  /**
   * Check if alert is active
   */
  isActiveAlert(): boolean {
    return this.eventData.alertData.status === 'open' ||
           this.eventData.alertData.status === 'investigating';
  }

  /**
   * Check if alert has correlation
   */
  hasCorrelation(): boolean {
    return !!this.eventData.correlation &&
           this.eventData.correlation.relatedAlerts.length > 0;
  }

  /**
   * Check if sync was successful
   */
  isSyncSuccessful(): boolean {
    return this.eventData.integration.syncStatus === 'success';
  }

  /**
   * Check if alert affects multiple hosts
   */
  affectsMultipleHosts(): boolean {
    return this.eventData.alertData.affectedHosts.length > 1;
  }

  /**
   * Check if alert involves user accounts
   */
  involvesUserAccounts(): boolean {
    return this.eventData.alertData.userAccounts.length > 0;
  }

  /**
   * Get alert priority
   */
  getAlertPriority(): 'low' | 'medium' | 'high' | 'critical' {
    const riskScore = this.eventData.alertData.riskScore;
    const severity = this.eventData.alertData.severity;

    if (severity === ThreatSeverity.CRITICAL || riskScore >= 90) {
      return 'critical';
    } else if (severity === ThreatSeverity.HIGH || riskScore >= 70) {
      return 'high';
    } else if (severity === ThreatSeverity.MEDIUM || riskScore >= 40) {
      return 'medium';
    } else {
      return 'low';
    }
  }

  /**
   * Get integration actions to trigger
   */
  getIntegrationActions(): string[] {
    const actions: string[] = [];

    if (this.eventData.eventType === 'alert_created') {
      actions.push('create_internal_event', 'apply_enrichment', 'check_correlations');
    }

    if (this.eventData.eventType === 'alert_updated') {
      actions.push('update_internal_event', 'sync_status_change');
    }

    if (this.eventData.eventType === 'alert_closed') {
      actions.push('close_internal_event', 'update_metrics');
    }

    if (this.hasCorrelation()) {
      actions.push('create_correlation_event', 'trigger_investigation');
    }

    if (this.isHighSeverity() && this.isActiveAlert()) {
      actions.push('escalate_alert', 'notify_soc', 'trigger_response');
    }

    if (!this.isSyncSuccessful()) {
      actions.push('retry_sync', 'log_sync_error', 'notify_admin');
    }

    return actions;
  }

  /**
   * Get notification requirements
   */
  getNotificationRequirements(): {
    urgency: 'low' | 'normal' | 'high' | 'critical';
    channels: string[];
    recipients: string[];
  } {
    const priority = this.getAlertPriority();
    let urgency: 'low' | 'normal' | 'high' | 'critical' = 'normal';
    const channels: string[] = ['dashboard'];
    const recipients: string[] = ['soc_analysts'];

    switch (priority) {
      case 'critical':
        urgency = 'critical';
        channels.push('sms', 'phone', 'slack', 'email');
        recipients.push('incident_response_team', 'security_manager');
        break;
      case 'high':
        urgency = 'high';
        channels.push('slack', 'email');
        recipients.push('senior_analysts');
        break;
      case 'medium':
        urgency = 'normal';
        channels.push('email');
        break;
      case 'low':
        urgency = 'low';
        break;
    }

    if (this.hasCorrelation()) {
      recipients.push('threat_hunters');
    }

    if (!this.isSyncSuccessful()) {
      recipients.push('integration_admin');
      channels.push('email');
    }

    return { urgency, channels, recipients };
  }

  /**
   * Get enrichment opportunities
   */
  getEnrichmentOpportunities(): Array<{
    type: string;
    description: string;
    priority: 'low' | 'medium' | 'high';
  }> {
    const opportunities: Array<{
      type: string;
      description: string;
      priority: 'low' | 'medium' | 'high';
    }> = [];

    if (this.eventData.alertData.sourceIPs.length > 0) {
      opportunities.push({
        type: 'ip_enrichment',
        description: 'Enrich source IPs with geolocation and reputation data',
        priority: 'high',
      });
    }

    if (this.involvesUserAccounts()) {
      opportunities.push({
        type: 'user_enrichment',
        description: 'Enrich user accounts with behavioral analysis',
        priority: 'medium',
      });
    }

    if (this.affectsMultipleHosts()) {
      opportunities.push({
        type: 'host_enrichment',
        description: 'Enrich affected hosts with asset information',
        priority: 'medium',
      });
    }

    if (this.hasCorrelation()) {
      opportunities.push({
        type: 'correlation_enrichment',
        description: 'Enrich with related attack patterns and TTPs',
        priority: 'high',
      });
    }

    return opportunities;
  }

  /**
   * Get sync metrics
   */
  getSyncMetrics(): {
    processingDuration: number;
    syncStatus: string;
    errorCount: number;
    enrichmentCount: number;
    mappingCount: number;
  } {
    return {
      processingDuration: this.eventData.integration.processingDuration,
      syncStatus: this.eventData.integration.syncStatus,
      errorCount: this.eventData.integration.errors.length,
      enrichmentCount: this.eventData.integration.enrichmentApplied.length,
      mappingCount: this.eventData.integration.mappingApplied.length,
    };
  }

  /**
   * Convert to SIEM format
   */
  toSIEMFormat(): {
    alert: {
      id: string;
      title: string;
      description: string;
      severity: string;
      status: string;
      category: string;
      type: string;
      riskScore: number;
      eventCount: number;
      timestamps: {
        firstSeen: string;
        lastSeen: string;
        createdAt: string;
        updatedAt: string;
      };
    };
    sources: {
      ips: string[];
      hosts: string[];
      users: string[];
    };
    destinations: {
      ips: string[];
    };
    detection: {
      ruleId: string;
      ruleName: string;
      ruleType: string;
      confidence: number;
    };
    correlation?: {
      relatedAlerts: string[];
      correlationRules: string[];
      confidence: number;
      timeline: Array<{
        timestamp: string;
        event: string;
        description: string;
      }>;
    };
    metadata: {
      platform: string;
      instanceId: string;
      integrationId: string;
      processedAt: string;
      priority: string;
      actions: string[];
    };
  } {
    return {
      alert: {
        id: this.eventData.alertData.siemAlertId,
        title: this.eventData.alertData.title,
        description: this.eventData.alertData.description,
        severity: this.eventData.alertData.severity,
        status: this.eventData.alertData.status,
        category: this.eventData.alertData.category,
        type: this.eventData.alertData.type,
        riskScore: this.eventData.alertData.riskScore,
        eventCount: this.eventData.alertData.eventCount,
        timestamps: this.eventData.alertData.timestamps,
      },
      sources: {
        ips: this.eventData.alertData.sourceIPs,
        hosts: this.eventData.alertData.affectedHosts,
        users: this.eventData.alertData.userAccounts,
      },
      destinations: {
        ips: this.eventData.alertData.destinationIPs,
      },
      detection: this.eventData.alertData.detectionRule,
      correlation: this.eventData.correlation,
      metadata: {
        platform: this.eventData.siemPlatform.platform,
        instanceId: this.eventData.siemPlatform.instanceId,
        integrationId: this.eventData.integration.integrationId,
        processedAt: this.eventData.integration.processedAt,
        priority: this.getAlertPriority(),
        actions: this.getIntegrationActions(),
      },
    };
  }

  /**
   * Create from SIEM alert data
   */
  static fromSIEMAlert(
    siemAlert: any,
    platform: string,
    integrationId: string,
    direction: 'inbound' | 'outbound' | 'bidirectional' = 'inbound'
  ): SIEMIntegrationEvent {
    const now = new Date();

    const eventData: SIEMIntegrationEventData = {
      eventType: 'alert_created',
      siemPlatform: {
        platform,
        version: siemAlert.platform_version || '1.0',
        instanceId: siemAlert.instance_id || 'default',
        endpoint: siemAlert.endpoint || '',
      },
      alertData: {
        siemAlertId: siemAlert.id || siemAlert.alert_id,
        internalEventId: siemAlert.internal_event_id,
        title: siemAlert.title || siemAlert.name,
        description: siemAlert.description || '',
        severity: siemAlert.severity || ThreatSeverity.MEDIUM,
        status: siemAlert.status || 'open',
        category: siemAlert.category || 'unknown',
        type: siemAlert.type || 'unknown',
        sourceIPs: siemAlert.source_ips || [],
        destinationIPs: siemAlert.destination_ips || [],
        affectedHosts: siemAlert.affected_hosts || [],
        userAccounts: siemAlert.user_accounts || [],
        detectionRule: {
          ruleId: siemAlert.rule_id || '',
          ruleName: siemAlert.rule_name || '',
          ruleType: siemAlert.rule_type || '',
          confidence: siemAlert.confidence || 50,
        },
        timestamps: {
          firstSeen: siemAlert.first_seen || now.toISOString(),
          lastSeen: siemAlert.last_seen || now.toISOString(),
          createdAt: siemAlert.created_at || now.toISOString(),
          updatedAt: siemAlert.updated_at || now.toISOString(),
        },
        rawData: siemAlert.raw_data || {},
        eventCount: siemAlert.event_count || 1,
        riskScore: siemAlert.risk_score || 50,
      },
      correlation: siemAlert.correlation,
      integration: {
        integrationId,
        direction,
        processedAt: now.toISOString(),
        processingDuration: 0,
        mappingApplied: [],
        enrichmentApplied: [],
        syncStatus: 'success',
        errors: [],
      },
    };

    return new SIEMIntegrationEvent(eventData);
  }
}
